# AI关卡生成器测试指南

## 修复内容

已根据正确的Gemini API格式修复了以下问题：

### 0. JSON序列化问题修复
- ✅ 修复了Unity JsonUtility不支持匿名对象的问题
- ✅ 创建了具体的类来替代匿名对象
- ✅ 现在JSON请求应该能正确生成

### 1. API端点修正
- ✅ 使用正确的端点：`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`
- ✅ 使用`x-goog-api-key`头部进行认证（而不是Bearer token）

### 2. 请求格式修正
```json
{
  "contents": [
    {
      "parts": [
        {
          "text": "你的prompt内容"
        }
      ]
    }
  ]
}
```

### 3. 响应格式修正
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "AI返回的内容"
          }
        ]
      }
    }
  ]
}
```

## 测试步骤

### 1. 验证JSON格式
1. 在Unity编辑器中选择 `Tools > 快速JSON测试`
2. 查看Console输出，确认JSON格式正确
3. 应该看到类似这样的输出：
   ```json
   {
       "contents": [
           {
               "parts": [
                   {
                       "text": "测试文本"
                   }
               ]
           }
       ]
   }
   ```

### 2. 测试API调用
1. 打开关卡编辑器：`Tools > 连连看关卡编辑器`
2. 输入你的Gemini API密钥
3. 输入图案描述（如"心形"）
4. 点击"AI生成布局"
5. 查看Console输出的调试信息

### 3. 调试信息说明
代码会输出以下调试信息：
- `API URL`: 确认使用正确的端点
- `请求JSON`: 确认请求格式正确
- `API Key`: 显示密钥前10位（用于验证）
- 如果失败，会显示详细的错误信息和响应内容

## 常见问题排查

### 400 Bad Request
可能原因：
1. API密钥无效或格式错误
2. 请求JSON格式不正确
3. 网络连接问题

### 403 Forbidden
- 检查API密钥是否有效
- 确认API密钥有调用权限

### 网络连接问题
- 如果需要使用代理，可以考虑：
  1. 设置系统代理
  2. 使用VPN
  3. 后续可以添加代理支持

## 成功标志
如果一切正常，你应该看到：
1. Console中显示正确的API URL和请求JSON
2. 没有HTTP错误
3. 关卡编辑器中显示新生成的布局
4. 弹出"AI生成的xxx布局已应用！"对话框

## 下一步
如果基本功能正常工作，可以考虑：
1. 优化prompt以获得更好的生成效果
2. 添加更多图案类型
3. 实现代理支持（如果需要）
4. 添加生成参数调节功能
