using UnityEngine;
using UnityEditor;

public class QuickJSONTest
{
    [MenuItem("Tools/快速JSON测试")]
    public static void TestJSONSerialization()
    {
        // 测试请求JSON序列化
        var request = new LevelEditorWindow.GeminiRequest
        {
            contents = new LevelEditorWindow.GeminiRequestContent[]
            {
                new LevelEditorWindow.GeminiRequestContent
                {
                    parts = new LevelEditorWindow.GeminiRequestPart[]
                    {
                        new LevelEditorWindow.GeminiRequestPart { text = "测试文本" }
                    }
                }
            }
        };

        string requestJson = JsonUtility.ToJson(request, true);
        Debug.Log($"请求JSON:\n{requestJson}");

        // 测试响应JSON解析
        string responseJson = @"{
            ""candidates"": [
                {
                    ""content"": {
                        ""parts"": [
                            {
                                ""text"": ""0,0,1,1,1,0,0,0""
                            }
                        ]
                    }
                }
            ]
        }";

        var response = JsonUtility.FromJson<LevelEditorWindow.GeminiResponse>(responseJson);
        if (response?.candidates != null && response.candidates.Length > 0)
        {
            Debug.Log($"响应解析成功: {response.candidates[0].content.parts[0].text}");
        }
        else
        {
            Debug.LogError("响应解析失败");
        }
    }
}
